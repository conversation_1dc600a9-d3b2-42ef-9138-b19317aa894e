import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";

const Faq = () => {
  const faqs = [
    {
      question: "What facial factors does the AI analyze for looksmax improvement?",
      answer:
        "Our AI analyzes 9 key factors prioritized by looksmax principles: facial symmetry, jawlines, cheekbones, face shape, eyes, eyebrow shape, skin quality, masculinity, and hairstyle. The analysis focuses on bone structure (highest priority), improvable features (medium priority), and styling choices (lower priority) to give you actionable insights.",
    },
    {
      question: "How accurate and scientific is the facial analysis?",
      answer:
        "Our AI uses computer vision technology trained on scientific beauty research, including golden ratio proportions, facial symmetry studies, and established attractiveness markers. While beauty is subjective, our analysis provides objective measurements based on peer-reviewed research to help you understand your facial characteristics.",
    },
    {
      question: "What's the difference between 'softmaxxing' and 'hardmaxxing' improvements?",
      answer:
        "Softmaxxing refers to non-surgical improvements you can make: better skincare, grooming, hairstyles, fitness, posture, and style choices. These are accessible, affordable changes that can significantly boost your attractiveness. Hardmaxxing involves surgical procedures. Our analysis focuses primarily on softmaxxing opportunities to help you maximize your natural potential.",
    },
    {
      question: "Can someone who's 'sub-5' actually improve to HTN (High Tier Normie) or above?",
      answer:
        "Absolutely! Many people underestimate their potential. Through strategic softmaxxing - improving skin quality, optimizing hairstyles, better grooming, fitness, and learning good photography angles - significant improvements are possible. Our analysis identifies your specific improvement opportunities and provides a realistic roadmap for enhancement.",
    },
    {
      question: "Is this website safe and secure?",
      answer:
        "Yes, our website is completely safe to use. We employ industry-standard security measures and never require users to upload or store images for analysis. All processing is done securely in the browser or on our servers with temporary processing only.",
    },
    {
      question: "Is the attractiveness score analysis free?",
      answer:
        "Yes, our basic attractiveness score analysis is completely free to use. Simply upload a photo and get instant results with detailed insights into your facial features and improvement recommendations at no cost.",
    },
    {
      question: "Do you sell or share my data?",
      answer:
        "No, we do not sell or share your data with any third parties. Your privacy is our priority, and we only process the uploaded images temporarily for analysis purposes. No images are stored permanently on our servers.",
    },
  ];

  return (
    <section id="faq" className="py-32 bg-gray-50">
      <div className="container">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-center">
          FAQ
        </h2>
        {faqs.map((faq, index) => (
          <Accordion key={index} type="single" collapsible>
            <AccordionItem value={`item-${index}`}>
              <AccordionTrigger className="hover:text-foreground/60 hover:no-underline text-lg">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-normal">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        ))}
      </div>
    </section>
  );
};

export default Faq;
