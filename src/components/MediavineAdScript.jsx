'use client'

import Script from 'next/script'

export default function MediavineAdScript() {
  return null;
  // Temporarily commented out to fix upload error
  // return (
  //   <Script
  //     id="mediavine-script"
  //     strategy="afterInteractive"
  //     dangerouslySetInnerHTML={{
  //       __html: `
  //         (function() {
  //           var script = document.createElement('script');
  //           script.src = '//scripts.scriptwrapper.com/tags/e8f47751-9b24-4e59-bb46-6e0f11d0f6ad.js';
  //           script.type = 'text/javascript';
  //           script.async = true;
  //           script.setAttribute('data-noptimize', '1');
  //           script.setAttribute('data-cfasync', 'false');
  //           script.setAttribute('data-adsense-visible', 'true');
  //           document.head.appendChild(script);
  //           console.log('Mediavine script loaded');
  //         })();
  //       `
  //     }}
  //   />
  // )
}
