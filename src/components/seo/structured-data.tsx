import Script from 'next/script';

interface StructuredDataProps {
  type?: 'website' | 'webapp' | 'article';
}

export default function StructuredData({ type = 'webapp' }: StructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://attractive-score.vercel.app';
  
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AttractivenessScore",
    "alternateName": "How Attractive Am I",
    "url": baseUrl,
    "description": "AI-powered facial analysis and attractiveness scoring tool with detailed insights and personalized recommendations.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "AttractivenessScore",
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 512,
        "height": 512
      }
    }
  };

  const webAppSchema = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "AttractivenessScore - AI Facial Analysis",
    "alternateName": "How Attractive Am I",
    "url": baseUrl,
    "description": "Advanced AI-powered facial analysis tool that provides detailed attractiveness scoring, face shape detection, and golden ratio analysis with personalized beauty recommendations.",
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "featureList": [
      "AI-powered attractiveness scoring",
      "Facial proportions analysis",
      "Face shape detection",
      "Golden ratio analysis",
      "Personalized beauty recommendations",
      "Instant results",
      "Privacy-focused (no data storage)"
    ],
    "screenshot": {
      "@type": "ImageObject",
      "url": `${baseUrl}/screenshot.jpg`,
      "width": 1200,
      "height": 800
    },
    "publisher": {
      "@type": "Organization",
      "name": "AttractivenessScore",
      "url": baseUrl
    },
    "author": {
      "@type": "Organization",
      "name": "AttractivenessScore Team"
    },
    "datePublished": "2024-01-01",
    "dateModified": new Date().toISOString().split('T')[0],
    "inLanguage": "en-US",
    "isAccessibleForFree": true,
    "usageInfo": `${baseUrl}/terms-of-service`,
    "privacyPolicy": `${baseUrl}/privacy-policy`
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AttractivenessScore",
    "url": baseUrl,
    "logo": {
      "@type": "ImageObject",
      "url": `${baseUrl}/logo.png`,
      "width": 512,
      "height": 512
    },
    "description": "Leading provider of AI-powered facial analysis and beauty assessment tools.",
    "foundingDate": "2024",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://twitter.com/attractivescore"
    ]
  };

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How attractive am I? How does the AI attractiveness analysis work?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our 'How Attractive Am I' AI analyzer uses advanced computer vision algorithms to evaluate facial features including proportions, symmetry, skin quality, and overall harmony. The attractiveness analysis provides detailed scoring across multiple factors based on established beauty standards and scientific research."
        }
      },
      {
        "@type": "Question",
        "name": "What is the face shape detector and how accurate is it?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our face shape detector uses AI to identify your facial structure and categorize it into common face shapes like oval, round, square, heart, or diamond. The shape detector analyzes key facial measurements and provides styling tips specific to your detected face shape with high accuracy."
        }
      },
      {
        "@type": "Question",
        "name": "Is my photo stored or shared when I ask 'how attractive am I'?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "No, we prioritize your privacy completely. When you upload a photo asking 'how attractive am I', your photos are processed in real-time and are never stored on our servers. The attractiveness analysis is performed instantly and your image data is immediately discarded after processing."
        }
      },
      {
        "@type": "Question",
        "name": "What analysis types are available besides 'how attractive am I'?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Besides our main 'How Attractive Am I' analysis, we offer a comprehensive face shape detector that identifies your facial structure, and golden ratio analysis that measures classical beauty proportions. Each analysis type provides unique insights and personalized recommendations."
        }
      },
      {
        "@type": "Question",
        "name": "Is the 'how attractive am I' service free to use?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our 'How Attractive Am I' facial analysis service is completely free to use. Simply upload your photo and receive instant AI-powered insights about your facial features, attractiveness score, and face shape detection results."
        }
      },
      {
        "@type": "Question",
        "name": "How does the face shape detector help with styling?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Our face shape detector not only identifies your face shape but also provides personalized styling recommendations including hairstyles, makeup tips, and accessory suggestions that complement your specific facial structure for enhanced attractiveness."
        }
      }
    ]
  };

  const schemas = [websiteSchema, organizationSchema, faqSchema];
  
  if (type === 'webapp') {
    schemas.push(webAppSchema);
  }

  return (
    <>
      {schemas.map((schema, index) => (
        <Script
          key={index}
          id={`structured-data-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema),
          }}
        />
      ))}
    </>
  );
}
